<template>
  <div class="symptom-grid-example">
    <h2>症状选择器示例</h2>
    <p>这是新的传统表格样式症状选择器的使用示例</p>
    
    <!-- 使用新的症状网格选择器 -->
    <SymptomGridSelector :disabled="false" />
    
    <!-- 或者，如果您想要替换原有的症状列表，可以这样做： -->
    <!-- <SymptomGridSelector :disabled="disabled" /> -->
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import SymptomGridSelector from './SymptomGridSelector.vue';

// 如果需要控制禁用状态
const disabled = ref(false);
</script>

<style lang="less" scoped>
.symptom-grid-example {
  padding: 20px;
  
  h2 {
    margin-bottom: 8px;
    color: #262626;
  }
  
  p {
    margin-bottom: 20px;
    color: #8c8c8c;
  }
}
</style>
