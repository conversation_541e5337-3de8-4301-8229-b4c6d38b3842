<template>
  <a-card title="症状选择器" size="small">
    <template #extra>
      <a-space>
        <a-button type="default" size="small" @click="loadAllDictionaries" :loading="symptomDictLoading || severityDictLoading">
          <template #icon>
            <SearchOutlined />
          </template>
          刷新字典
        </a-button>
        <a-button type="primary" size="small" @click="handleSave" :loading="saving" :disabled="disabled">
          <template #icon>
            <SaveOutlined />
          </template>
          保存症状
        </a-button>
        <a-button type="default" size="small" @click="handleClear" :disabled="disabled">
          清空选择
        </a-button>
      </a-space>
    </template>

    <!-- 搜索框 -->
    <div class="search-section">
      <a-input-search
        v-model:value="searchKeyword"
        placeholder="输入症状名称快速搜索..."
        size="small"
        :allow-clear="true"
        @search="handleSearch"
        @input="handleSearch"
        style="width: 300px"
      />
      <div v-if="searchKeyword.trim()" class="search-stats">
        找到 {{ filteredSymptomCategories.length }} 个分类，共 {{ totalFilteredSymptoms }} 个症状
      </div>
    </div>

    <!-- 症状表格 -->
    <div class="symptom-grid-container">
      <div v-if="symptomDictLoading" class="loading-state">
        <a-spin size="large" />
        <div style="margin-top: 16px">加载症状字典中...</div>
      </div>
      
      <div v-else-if="!symptomDictLoading && symptomCategories.length === 0" class="no-data">
        <a-empty description="暂无症状字典数据">
          <template #extra>
            <a-button type="primary" size="small" @click="loadSymptomDict">重新加载</a-button>
          </template>
        </a-empty>
      </div>

      <div v-else-if="searchKeyword.trim() && filteredSymptomCategories.length === 0" class="no-data">
        <a-empty description="未找到匹配的症状" />
      </div>

      <div v-else class="symptom-categories">
        <div 
          v-for="category in filteredSymptomCategories" 
          :key="category.category"
          class="category-section"
        >
          <div class="category-header">
            <h4>{{ category.category }}</h4>
            <span class="symptom-count">({{ category.symptoms.length }}个症状)</span>
          </div>
          
          <div class="symptom-grid">
            <div
              v-for="symptom in category.symptoms"
              :key="symptom.id"
              class="symptom-cell"
              :class="getSymptomCellClass(symptom.id)"
              @click="toggleSymptom(symptom)"
            >
              <div class="symptom-text" v-html="highlightKeyword(symptom.dictText)"></div>
              <div v-if="getSymptomSeverity(symptom.id)" class="severity-indicator">
                {{ getSeverityText(getSymptomSeverity(symptom.id)) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 已选择症状汇总 -->
    <div v-if="selectedSymptoms.length > 0" class="selected-summary">
      <div class="summary-header">
        <h4>已选择症状 ({{ selectedSymptoms.length }})</h4>
      </div>
      <div class="selected-list">
        <a-tag
          v-for="item in selectedSymptoms"
          :key="item.symptom"
          :color="getSeverityColor(item.severity)"
          closable
          @close="removeSymptom(item.symptom)"
        >
          {{ getSymptomText(item.symptom) }} - {{ getSeverityText(item.severity) }}
        </a-tag>
      </div>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
import { ref, reactive, inject, watch, onMounted, computed, nextTick } from 'vue';
import { SearchOutlined, SaveOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useMessage } from '@/hooks/web/useMessage';
import { zyInquirySymptomList, zyInquirySymptomSaveOrUpdate, zyInquirySymptomDelete } from '../ZyInquiry.api';
import { getBySystem } from '../ZySymptomDict.api';
import { ajaxGetDictItems } from '../../system/menu/menu.api';

// 症状严重程度枚举
const SEVERITY_LEVELS = {
  NONE: '',
  LIGHT: '轻',
  SEVERE: '重'
};

// 症状严重程度颜色映射
const SEVERITY_COLORS = {
  [SEVERITY_LEVELS.LIGHT]: 'orange',
  [SEVERITY_LEVELS.SEVERE]: 'red'
};

interface SymptomRecord {
  id?: string;
  symptom: string;
  severity: string;
  remark: string;
  inquiryId: string;
}

// Props
interface Props {
  disabled?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

// Injected values
const inquiryMainId = inject('inquiryMainId', ref(''));
const inquiryReady = inject('inquiryReady', ref(false));

// State
const loading = ref(false);
const saving = ref(false);
const searchKeyword = ref<string>('');
const symptomDictLoading = ref(false);
const severityDictLoading = ref(false);
const symptomCategories = ref([]);
const severityDict = ref([]);
const selectedSymptoms = ref<SymptomRecord[]>([]);

// 计算属性
const symptomDict = computed(() => {
  if (!symptomCategories.value || !Array.isArray(symptomCategories.value)) {
    return [];
  }
  
  return symptomCategories.value
    .filter(category => category && typeof category === 'object' && Array.isArray(category.symptoms))
    .flatMap(category => 
      category.symptoms
        .filter(symptom => symptom && typeof symptom === 'object' && symptom.id && symptom.dictText)
        .map(symptom => ({
          id: symptom.id,
          dictText: symptom.dictText,
          isSpecial: Boolean(category.isSpecial || symptom.isSpecial),
          defaultFlag: symptom.defaultFlag,
        }))
    )
    .filter(symptom => symptom.id && symptom.dictText);
});

const filteredSymptomCategories = computed(() => {
  if (!symptomCategories.value || !Array.isArray(symptomCategories.value)) {
    return [];
  }

  const safeCategories = symptomCategories.value
    .filter(category => category && typeof category === 'object' && category.category && Array.isArray(category.symptoms))
    .map(category => ({
      ...category,
      symptoms: (category.symptoms || []).filter(symptom => 
        symptom && typeof symptom === 'object' && symptom.id && symptom.dictText
      ),
    }))
    .filter(category => category.symptoms.length > 0);

  if (!searchKeyword.value || !searchKeyword.value.trim()) {
    return safeCategories;
  }

  const keyword = searchKeyword.value.toLowerCase().trim();
  return safeCategories
    .map(category => ({
      ...category,
      symptoms: category.symptoms.filter(symptom => {
        const text = symptom.dictText.toLowerCase();
        const id = symptom.id.toLowerCase();
        return text.includes(keyword) || id.includes(keyword);
      }),
    }))
    .filter(category => category.symptoms && category.symptoms.length > 0);
});

const totalFilteredSymptoms = computed(() => {
  return filteredSymptomCategories.value.reduce((sum, cat) => sum + cat.symptoms.length, 0);
});

// 方法
const getSymptomText = (symptomId: string) => {
  if (!symptomId || !symptomDict.value || !Array.isArray(symptomDict.value)) {
    return symptomId || '';
  }
  const symptom = symptomDict.value.find(s => s && s.id === symptomId);
  return symptom ? symptom.dictText : symptomId;
};

const getSeverityText = (severityValue: string) => {
  if (!severityValue) return '';
  if (!severityDict.value || !Array.isArray(severityDict.value)) {
    return severityValue;
  }
  const severity = severityDict.value.find(s => s && s.value === severityValue);
  return severity ? severity.text : severityValue;
};

const getSeverityColor = (severityValue: string) => {
  return SEVERITY_COLORS[severityValue] || 'default';
};

const getSymptomSeverity = (symptomId: string) => {
  const selected = selectedSymptoms.value.find(item => item.symptom === symptomId);
  return selected ? selected.severity : '';
};

const getSymptomCellClass = (symptomId: string) => {
  const severity = getSymptomSeverity(symptomId);
  return {
    'selected-light': severity === SEVERITY_LEVELS.LIGHT,
    'selected-severe': severity === SEVERITY_LEVELS.SEVERE,
    'unselected': !severity
  };
};

const toggleSymptom = (symptom: any) => {
  if (props.disabled) return;
  
  const existingIndex = selectedSymptoms.value.findIndex(item => item.symptom === symptom.id);
  
  if (existingIndex === -1) {
    // 未选中 -> 轻度
    selectedSymptoms.value.push({
      symptom: symptom.id,
      severity: SEVERITY_LEVELS.LIGHT,
      remark: '',
      inquiryId: inquiryMainId.value,
    });
  } else {
    const existing = selectedSymptoms.value[existingIndex];
    if (existing.severity === SEVERITY_LEVELS.LIGHT) {
      // 轻度 -> 重度
      existing.severity = SEVERITY_LEVELS.SEVERE;
    } else {
      // 重度 -> 未选中
      selectedSymptoms.value.splice(existingIndex, 1);
    }
  }
};

const removeSymptom = (symptomId: string) => {
  const index = selectedSymptoms.value.findIndex(item => item.symptom === symptomId);
  if (index !== -1) {
    selectedSymptoms.value.splice(index, 1);
  }
};

const handleClear = () => {
  selectedSymptoms.value = [];
  message.success('已清空所有选择');
};

const handleSearch = () => {
  // 搜索逻辑通过computed属性实现
};

const highlightKeyword = (text: string) => {
  if (!text || typeof text !== 'string' || !searchKeyword.value.trim()) {
    return text || '';
  }
  const keyword = searchKeyword.value.trim();
  const regex = new RegExp(`(${keyword})`, 'gi');
  return text.replace(regex, '<mark style="background-color: #fff3cd; padding: 0 2px; border-radius: 2px;">$1</mark>');
};

// 加载症状字典数据
async function loadSymptomDict() {
  try {
    symptomDictLoading.value = true;
    const response = await getBySystem();

    let result = response;
    if (response && response.result) {
      result = response.result;
    }

    if (result && Array.isArray(result)) {
      const categories = result
        .filter(systemVO => systemVO && typeof systemVO === 'object')
        .map(systemVO => {
          const isSpecial = systemVO.defaultFlag === '1' || systemVO.systemName === '常用';

          return {
            category: systemVO.systemName || '',
            isSpecial: isSpecial,
            symptoms: (systemVO.symptomList || [])
              .filter(symptom => symptom && symptom.id && symptom.dictText)
              .map(symptom => ({
                id: symptom.id,
                dictText: symptom.dictText,
                isSpecial: isSpecial && symptom.defaultFlag == '1',
                defaultFlag: symptom.defaultFlag,
              })),
          };
        })
        .filter(category => category.category && category.symptoms.length > 0);

      categories.sort((a, b) => {
        const aSystem = result.find(s => s && s.systemName === a.category);
        const bSystem = result.find(s => s && s.systemName === b.category);
        return (aSystem?.sortOrder || 0) - (bSystem?.sortOrder || 0);
      });

      symptomCategories.value = categories;
      console.log('症状字典加载成功:', categories.length, '个系统');
    } else {
      console.warn('症状字典数据格式异常:', response);
      symptomCategories.value = [];
    }
  } catch (error) {
    console.error('加载症状字典失败:', error);
    message.error('加载症状字典失败，请检查网络连接或联系管理员');
    symptomCategories.value = [];
  } finally {
    symptomDictLoading.value = false;
  }
}

// 加载症状程度字典数据
async function loadSeverityDict() {
  try {
    severityDictLoading.value = true;
    const response = await ajaxGetDictItems({ code: 'symptomSeverity' });

    if (response && Array.isArray(response)) {
      severityDict.value = response
        .filter(item => item && typeof item === 'object' && item.value && item.text)
        .map(item => ({
          value: item.value,
          text: item.text,
          sort: item.sort || 0,
        }))
        .sort((a, b) => a.sort - b.sort);
      console.log('症状程度字典加载成功:', severityDict.value.length, '项');
    } else {
      console.warn('症状程度字典数据格式异常:', response);
      severityDict.value = [
        { value: '轻', text: '轻', sort: 1 },
        { value: '重', text: '重', sort: 2 },
      ];
    }
  } catch (error) {
    console.error('加载症状程度字典失败:', error);
    message.error('加载症状程度字典失败，将使用默认选项');
    severityDict.value = [
      { value: '轻', text: '轻', sort: 1 },
      { value: '重', text: '重', sort: 2 },
    ];
  } finally {
    severityDictLoading.value = false;
  }
}

// 加载所有字典
async function loadAllDictionaries() {
  await Promise.all([loadSymptomDict(), loadSeverityDict()]);
}

// 加载已选择的症状数据
async function loadData() {
  if (!inquiryMainId.value) return;

  try {
    loading.value = true;
    const result = await zyInquirySymptomList({ inquiryId: inquiryMainId.value });

    if (result && result.records && Array.isArray(result.records)) {
      const validRecords = result.records.filter(item =>
        item && typeof item === 'object' && item.symptom
      );

      selectedSymptoms.value = validRecords.map(item => ({
        id: item.id || '',
        symptom: item.symptom || '',
        severity: item.severity || '',
        remark: item.remark || '',
        inquiryId: item.inquiryId || inquiryMainId.value,
      }));

      console.log('已选择症状数据加载完成:', selectedSymptoms.value.length);
    } else {
      selectedSymptoms.value = [];
    }
  } catch (error) {
    console.error('加载症状记录失败:', error);
    message.error('加载数据失败');
    selectedSymptoms.value = [];
  } finally {
    loading.value = false;
  }
}

// 保存症状数据
async function handleSave() {
  if (props.disabled) return;

  try {
    saving.value = true;

    // 获取当前数据库中的症状记录
    const currentResult = await zyInquirySymptomList({ inquiryId: inquiryMainId.value });
    const currentRecords = currentResult?.records || [];

    // 删除不再选择的症状
    const deletePromises = currentRecords
      .filter(record => !selectedSymptoms.value.find(selected => selected.symptom === record.symptom))
      .map(record => {
        if (record.id) {
          return zyInquirySymptomDelete({ id: record.id }, () => {});
        }
        return Promise.resolve();
      });

    await Promise.all(deletePromises);

    // 保存或更新选择的症状
    const savePromises = selectedSymptoms.value.map(async (symptom) => {
      const existingRecord = currentRecords.find(record => record.symptom === symptom.symptom);
      const isUpdate = !!existingRecord?.id;

      const symptomText = getSymptomText(symptom.symptom);

      const saveData = {
        id: existingRecord?.id,
        symptom: symptom.symptom,
        symptomName: symptomText,
        severity: symptom.severity,
        remark: symptom.remark,
        inquiryId: inquiryMainId.value,
      };

      const result = await zyInquirySymptomSaveOrUpdate(saveData, isUpdate);

      if (result && result.success) {
        if (!isUpdate && result.result) {
          symptom.id = result.result.id || result.result;
        }
        return { success: true, symptom };
      } else {
        throw new Error(result?.message || '保存失败');
      }
    });

    await Promise.all(savePromises);

    message.success(`保存成功！共保存 ${selectedSymptoms.value.length} 个症状`);

    // 重新加载数据以确保同步
    await loadData();

  } catch (error) {
    console.error('保存症状失败:', error);
    message.error('保存失败：' + (error.message || '未知错误'));
  } finally {
    saving.value = false;
  }
}

// 监听器
watch(
  () => inquiryReady?.value,
  (ready) => {
    if (ready && inquiryMainId?.value) {
      loadData();
    }
  },
  { immediate: true }
);

watch(
  () => inquiryMainId?.value,
  (newId) => {
    if (newId && inquiryReady?.value) {
      loadData();
    }
  }
);

// 生命周期
onMounted(() => {
  console.log('症状网格选择器组件挂载');

  loadAllDictionaries().catch(error => {
    console.error('字典加载失败:', error);
  });

  if (inquiryMainId?.value && inquiryReady?.value) {
    loadData().catch(error => {
      console.error('数据加载失败:', error);
    });
  }
});
</script>

<style lang="less" scoped>
.search-section {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 16px;

  .search-stats {
    font-size: 12px;
    color: #1890ff;
    background: #f0f9ff;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #bae7ff;
  }
}

.symptom-grid-container {
  min-height: 400px;

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #8c8c8c;
    font-size: 14px;
  }

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
  }
}

.symptom-categories {
  .category-section {
    margin-bottom: 24px;

    .category-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 2px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .symptom-count {
        font-size: 12px;
        color: #8c8c8c;
        background: #f5f5f5;
        padding: 2px 6px;
        border-radius: 10px;
      }
    }

    .symptom-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: 8px;

      .symptom-cell {
        position: relative;
        min-height: 60px;
        padding: 8px 12px;
        border: 2px solid #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        background: #fff;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        // 未选中状态 - 白色背景
        &.unselected {
          background: #fff;
          border-color: #d9d9d9;

          &:hover {
            border-color: #1890ff;
            background: #f0f9ff;
          }
        }

        // 轻度症状 - 橙色背景
        &.selected-light {
          background: #fff7e6;
          border-color: #fa8c16;
          color: #d46b08;

          &:hover {
            background: #ffd591;
            border-color: #d46b08;
          }
        }

        // 重度症状 - 红色背景
        &.selected-severe {
          background: #fff2f0;
          border-color: #ff4d4f;
          color: #cf1322;

          &:hover {
            background: #ffccc7;
            border-color: #cf1322;
          }
        }

        .symptom-text {
          font-size: 13px;
          font-weight: 500;
          line-height: 1.4;
          word-break: break-all;

          :deep(mark) {
            background-color: #fff3cd;
            padding: 0 2px;
            border-radius: 2px;
          }
        }

        .severity-indicator {
          position: absolute;
          top: 4px;
          right: 4px;
          font-size: 10px;
          font-weight: 600;
          padding: 2px 4px;
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.9);
          border: 1px solid currentColor;
        }
      }
    }
  }
}

.selected-summary {
  margin-top: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;

  .summary-header {
    margin-bottom: 12px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }
  }

  .selected-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    :deep(.ant-tag) {
      margin: 0;
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;

      &.ant-tag-orange {
        background: #fff7e6;
        border-color: #fa8c16;
        color: #d46b08;
      }

      &.ant-tag-red {
        background: #fff2f0;
        border-color: #ff4d4f;
        color: #cf1322;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .symptom-categories .category-section .symptom-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 6px;

    .symptom-cell {
      min-height: 50px;
      padding: 6px 8px;

      .symptom-text {
        font-size: 12px;
      }
    }
  }
}
</style>
